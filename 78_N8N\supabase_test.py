import os
from dotenv import load_dotenv

def test_supabase_connection():
    """Test Supabase connection using Python client"""
    try:
        from supabase import create_client, Client
        
        # Load environment variables
        load_dotenv()
        
        # Get Supabase credentials
        url = os.environ.get("SUPABASE_URL")
        key = os.environ.get("SUPABASE_ANON_KEY")
        
        print("🧪 Testing Supabase Connection...\n")
        
        if not url:
            print("❌ SUPABASE_URL not found in .env file")
            return False
            
        if not key:
            print("❌ SUPABASE_ANON_KEY not found in .env file")
            return False
        
        print(f"🔗 Supabase URL: {url}")
        print(f"🔑 API Key: {key[:20]}...")
        print()
        
        # Create Supabase client
        print("1. Creating Supabase client...")
        supabase: Client = create_client(url, key)
        print("✅ Supabase client created successfully!")
        print()
        
        # Test 1: Try to list tables (this will work if connection is good)
        print("2. Testing basic API connectivity...")
        try:
            # This should return table information or an error about permissions
            result = supabase.table('documents').select("*").limit(1).execute()
            print("✅ API connection successful!")
            print(f"📊 Query executed successfully. Found {len(result.data)} rows")
            
            if len(result.data) > 0:
                print(f"📄 Sample data: {result.data[0]}")
            
        except Exception as api_error:
            print(f"⚠️  API connection works, but query failed: {api_error}")
            print("   This might be normal if the 'documents' table doesn't exist yet")
        
        print()
        
        # Test 2: Try to get database info using RPC
        print("3. Testing RPC functionality...")
        try:
            # Try a simple RPC call
            result = supabase.rpc('version').execute()
            print("✅ RPC calls work!")
            print(f"📊 Result: {result.data}")
        except Exception as rpc_error:
            print(f"⚠️  RPC failed: {rpc_error}")
            print("   This is normal - the 'version' function might not exist")
        
        print()
        
        # Test 3: Try to access storage (if available)
        print("4. Testing storage access...")
        try:
            buckets = supabase.storage.list_buckets()
            print("✅ Storage access works!")
            print(f"📦 Found {len(buckets)} storage buckets")
        except Exception as storage_error:
            print(f"⚠️  Storage access failed: {storage_error}")
            print("   This might be normal depending on your permissions")
        
        print()
        print("🎉 Overall Result: Supabase connection is working!")
        print("\n💡 You can use Supabase client for your application instead of direct PostgreSQL")
        
        return True
        
    except ImportError:
        print("❌ Supabase Python client not installed")
        print("   Run: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def show_supabase_usage_examples():
    """Show examples of how to use Supabase for common operations"""
    print("\n" + "="*60)
    print("📚 SUPABASE USAGE EXAMPLES")
    print("="*60)
    
    print("""
🔧 Basic Setup:
```python
import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_ANON_KEY")
supabase: Client = create_client(url, key)
```

📝 Insert Data:
```python
data = supabase.table('documents').insert({
    'content': 'Your document content',
    'metadata': {'file_id': 'some_id'},
    'embedding': [0.1, 0.2, 0.3, ...]  # Your vector
}).execute()
```

🔍 Query Data:
```python
result = supabase.table('documents').select("*").eq('id', 'some_id').execute()
```

🔎 Vector Search (if you have pgvector enabled):
```python
result = supabase.rpc('match_documents', {
    'query_embedding': [0.1, 0.2, 0.3, ...],
    'match_threshold': 0.8,
    'match_count': 10
}).execute()
```

🗑️ Delete Data:
```python
result = supabase.table('documents').delete().eq('id', 'some_id').execute()
```

📊 Raw SQL (using RPC):
```python
result = supabase.rpc('your_custom_function', {
    'param1': 'value1',
    'param2': 'value2'
}).execute()
```
""")

if __name__ == "__main__":
    success = test_supabase_connection()
    
    if success:
        show_supabase_usage_examples()
    else:
        print("\n💡 Troubleshooting:")
        print("1. Make sure you have the .env file in the same directory")
        print("2. Check that SUPABASE_URL and SUPABASE_ANON_KEY are set correctly")
        print("3. Install supabase: pip install supabase")
        print("4. Check your internet connection")
