import os
import psycopg2
import socket
import requests
from dotenv import load_dotenv

def test_internet_connectivity():
    """Test basic internet connectivity"""
    try:
        response = requests.get("https://www.google.com", timeout=5)
        print("✅ Internet connectivity: OK")
        return True
    except Exception as e:
        print(f"❌ Internet connectivity failed: {e}")
        return False

def test_supabase_api_connectivity():
    """Test if we can reach Supabase API"""
    load_dotenv()
    supabase_url = os.environ.get("SUPABASE_URL")

    if not supabase_url:
        print("❌ SUPABASE_URL not found in environment")
        return False

    try:
        response = requests.get(f"{supabase_url}/rest/v1/", timeout=10)
        print(f"✅ Supabase API reachable: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Supabase API unreachable: {e}")
        return False

def test_dns_with_alternatives():
    """Test DNS resolution with different approaches"""
    hostnames_to_try = [
        "db.csdzhpskeyqswqmffvxv.supabase.co",
        "csdzhpskeyqswqmffvxv.supabase.co",  # Try without 'db.' prefix
    ]

    for hostname in hostnames_to_try:
        try:
            ip = socket.gethostbyname(hostname)
            print(f"✅ DNS Resolution successful: {hostname} -> {ip}")
            return hostname, True
        except socket.gaierror as e:
            print(f"❌ DNS Resolution failed for {hostname}: {e}")

    return None, False

def test_supabase_client_connection():
    """Test connection using Supabase Python client"""
    try:
        from supabase import create_client, Client

        load_dotenv()
        url = os.environ.get("SUPABASE_URL")
        key = os.environ.get("SUPABASE_ANON_KEY")

        if not url or not key:
            print("❌ SUPABASE_URL or SUPABASE_ANON_KEY not found")
            return False

        print(f"🔗 Attempting Supabase client connection to: {url}")
        supabase: Client = create_client(url, key)

        # Test a simple query - try to list tables or get a simple response
        result = supabase.rpc('version').execute()
        print("✅ Supabase client connection successful!")
        return True

    except ImportError:
        print("❌ Supabase Python client not installed. Run: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Supabase client connection failed: {e}")
        return False

def test_alternative_database_url():
    """Test with alternative database URL format"""
    load_dotenv()

    # Try different URL formats
    alternative_urls = [
        # Original
        os.environ.get("DATABASE_URL"),
        # Try with different host format
        "postgresql://postgres:<EMAIL>:5432/postgres",
        # Try with SSL mode specified
        "postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require",
    ]

    for i, url in enumerate(alternative_urls):
        if not url:
            continue

        print(f"🔗 Trying URL format {i+1}: {url[:50]}...")
        try:
            conn = psycopg2.connect(url)
            print(f"✅ Connection successful with URL format {i+1}!")

            # Test a simple query
            cursor = conn.cursor()
            cursor.execute("SELECT 1;")
            result = cursor.fetchone()
            print(f"📊 Test query result: {result}")

            cursor.close()
            conn.close()
            return True

        except Exception as e:
            print(f"❌ URL format {i+1} failed: {e}")

    return False

if __name__ == "__main__":
    print("🧪 Comprehensive Supabase Connection Test...\n")

    # Test 1: Basic connectivity
    print("1. Testing Internet Connectivity:")
    internet_ok = test_internet_connectivity()
    print()

    # Test 2: Supabase API
    print("2. Testing Supabase API Connectivity:")
    api_ok = test_supabase_api_connectivity()
    print()

    # Test 3: DNS Resolution
    print("3. Testing DNS Resolution:")
    working_hostname, dns_ok = test_dns_with_alternatives()
    print()

    # Test 4: Supabase Client
    print("4. Testing Supabase Python Client:")
    client_ok = test_supabase_client_connection()
    print()

    # Test 5: Alternative Database URLs
    print("5. Testing Alternative Database URLs:")
    db_ok = test_alternative_database_url()
    print()

    # Summary and recommendations
    print("📋 Summary:")
    print(f"   Internet: {'✅' if internet_ok else '❌'}")
    print(f"   Supabase API: {'✅' if api_ok else '❌'}")
    print(f"   DNS Resolution: {'✅' if dns_ok else '❌'}")
    print(f"   Supabase Client: {'✅' if client_ok else '❌'}")
    print(f"   Database Connection: {'✅' if db_ok else '❌'}")

    print("\n💡 Recommendations:")
    if not internet_ok:
        print("   - Check your internet connection")
    elif not api_ok:
        print("   - Check if Supabase is experiencing outages")
        print("   - Verify your SUPABASE_URL in .env file")
    elif not dns_ok:
        print("   - Try changing your DNS servers to ******* or *******")
        print("   - Check if you're behind a corporate firewall")
        print("   - Verify the database hostname in your Supabase dashboard")
    elif client_ok and not db_ok:
        print("   - Use Supabase Python client instead of direct PostgreSQL connection")
        print("   - The client connection works, so use that for your application")
    elif not client_ok and not db_ok:
        print("   - Install Supabase client: pip install supabase")
        print("   - Check your Supabase credentials in .env file")