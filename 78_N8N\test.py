import psycopg2
import socket
import requests

def test_internet_connectivity():
    """Test basic internet connectivity"""
    try:
        response = requests.get("https://www.google.com", timeout=5)
        print("✅ Internet connectivity: OK")
        return True
    except Exception as e:
        print(f"❌ Internet connectivity failed: {e}")
        return False

def test_dns_resolution():
    """Test DNS resolution for Supabase hostname"""
    hostname = "db.csdzhpskeyqswqmffvxv.supabase.co"
    try:
        ip = socket.gethostbyname(hostname)
        print(f"✅ DNS Resolution successful: {hostname} -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"❌ DNS Resolution failed: {e}")
        return False

def test_connection_with_working_hostname():
    """Test connection using different hostname configurations"""

    # Test different connection configurations
    test_configs = [
        # NEW: Connection pooler (most likely to work)
        {"host": "aws-0-ap-southeast-1.pooler.supabase.com", "port": 5432, "user": "postgres.csdzhpskeyqswqmffvxv", "name": "Connection Pooler"},

        # Original configurations
        {"host": "csdzhpskeyqswqmffvxv.supabase.co", "port": 6543, "user": "postgres", "name": "Direct DB + Port 6543"},
        {"host": "csdzhpskeyqswqmffvxv.supabase.co", "port": 5432, "user": "postgres", "name": "Direct DB + Port 5432"},
        {"host": "db.csdzhpskeyqswqmffvxv.supabase.co", "port": 6543, "user": "postgres", "name": "Original hostname + Port 6543"},
        {"host": "db.csdzhpskeyqswqmffvxv.supabase.co", "port": 5432, "user": "postgres", "name": "Original hostname + Port 5432"},
    ]

    successful_configs = []

    for config in test_configs:
        try:
            print(f"🔗 Testing {config['name']}...")
            conn = psycopg2.connect(
                host=config["host"],
                port=config["port"],
                database="postgres",
                user=config["user"],
                password="LfcKnR44eYM5IMCn",
                sslmode="require",
                connect_timeout=10  # 10 second timeout
            )
            print(f"✅ SUCCESS: {config['name']} works!")

            # Test a simple query
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"📊 PostgreSQL version: {version[0][:60]}...")

            cursor.close()
            conn.close()
            successful_configs.append(config)

        except Exception as e:
            print(f"❌ FAILED: {config['name']} - {e}")

    return successful_configs



if __name__ == "__main__":
    print("🧪 Testing Supabase Connection with Correct Port...\n")

    # Test 1: Basic connectivity
    print("1. Testing Internet Connectivity:")
    internet_ok = test_internet_connectivity()
    print()

    # Test 2: DNS Resolution
    print("2. Testing DNS Resolution:")
    dns_ok = test_dns_resolution()
    print()

    # Test 3: Try all connection combinations
    print("3. Testing All PostgreSQL Connection Combinations:")
    successful_configs = test_connection_with_working_hostname()
    print()

    # Summary
    print("📋 Summary:")
    print(f"   Internet: {'✅' if internet_ok else '❌'}")
    print(f"   DNS Resolution: {'✅' if dns_ok else '❌'}")
    print(f"   Successful Connections: {len(successful_configs)}")

    print("\n💡 Result:")
    if successful_configs:
        print("   ✅ SUCCESS! Found working connection(s):")
        for config in successful_configs:
            print(f"      • {config['name']}")
            print(f"        Host: {config['host']}")
            print(f"        Port: {config['port']}")

        # Recommend the best option
        best_config = successful_configs[0]  # First successful one
        print(f"\n   📝 Recommended N8N Settings:")
        print(f"      Host: {best_config['host']}")
        print(f"      Port: {best_config['port']}")
        print(f"      Database: postgres")
        print(f"      User: postgres")
        print(f"      Password: LfcKnR44eYM5IMCn")
        print(f"      SSL Mode: require")
    else:
        print("   ❌ No working connections found")
        print("\n💡 Try these solutions:")
        print("   1. Check if you're behind a corporate firewall")
        print("   2. Try using a VPN")
        print("   3. Use Supabase API instead of direct PostgreSQL connection")
        print("   4. Verify credentials in your Supabase dashboard")