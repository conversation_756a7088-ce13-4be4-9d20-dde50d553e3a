import os
import psycopg2
import socket
from dotenv import load_dotenv

def test_dns_resolution():
    """Test if we can resolve the Supabase hostname"""
    hostname = "db.csdzhpskeyqswqmffvxv.supabase.co"
    try:
        ip = socket.gethostbyname(hostname)
        print(f"✅ DNS Resolution successful: {hostname} -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"❌ DNS Resolution failed: {e}")
        return False

def test_connection_with_url():
    """Test connection using DATABASE_URL"""
    load_dotenv()
    database_url = os.environ.get("DATABASE_URL")
    
    if not database_url:
        print("❌ DATABASE_URL not found in environment variables")
        return False
    
    print(f"🔗 Attempting connection with URL: {database_url[:50]}...")
    
    try:
        conn = psycopg2.connect(database_url)
        print("✅ Connection successful with DATABASE_URL!")
        
        # Test a simple query
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"📊 PostgreSQL version: {version[0][:50]}...")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def test_connection_with_components():
    """Test connection using individual components"""
    try:
        conn = psycopg2.connect(
            host="db.csdzhpskeyqswqmffvxv.supabase.co",
            port=5432,
            database="postgres",
            user="postgres",
            password="LfcKnR44eYM5IMCn",
            sslmode="require"  # Supabase requires SSL
        )
        print("✅ Connection successful with individual components!")
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection with components failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Supabase PostgreSQL Connection...\n")
    
    # Test 1: DNS Resolution
    print("1. Testing DNS Resolution:")
    dns_ok = test_dns_resolution()
    print()
    
    # Test 2: Connection with DATABASE_URL
    print("2. Testing connection with DATABASE_URL:")
    url_ok = test_connection_with_url()
    print()
    
    # Test 3: Connection with individual components (only if DNS works)
    if dns_ok:
        print("3. Testing connection with individual components:")
        components_ok = test_connection_with_components()
        print()
    
    # Summary
    print("📋 Summary:")
    print(f"   DNS Resolution: {'✅' if dns_ok else '❌'}")
    print(f"   DATABASE_URL: {'✅' if url_ok else '❌'}")
    if dns_ok:
        print(f"   Components: {'✅' if components_ok else '❌'}")