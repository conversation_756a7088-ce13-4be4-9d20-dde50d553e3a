# SAP iFlow AI Code Generation - Configuration Template
# Copy this to .env and fill in your actual values
 
# =============================================================================
# 🔑 REQUIRED API KEYS (You MUST configure these)
# =============================================================================
 
# RunPod API Key (CRITICAL - Required for cloud training & serverless)
RUNPOD_API_KEY=rpa_PC6SLEQEY1SD4PSJXZLV5RBFTKTJIIWRT461NHCB1ekzyh
# Get from: https://www.runpod.io/console/user/settings
 
# RunPod Serverless Endpoint IDs (for inference - CONFIGURED!)
# RUNPOD_CODELLAMA_ENDPOINT=fuep0cdc1bs3an
RUNPOD_CODELLAMA_ENDPOINT=l5dedjto3402qm
# RUNPOD_CODELLAMA_13B_ENDPOINT=https://api.runpod.ai/v2/fuep0cdc1bs3an/run
RUNPOD_CODELLAMA_13B_ENDPOINT=https://api.runpod.ai/v2/l5dedjto3402qm/run
RUNPOD_GEMMA3_12B_ENDPOINT=https://api.runpod.ai/v2/s5unaaduyy7otl/run
RUNPOD_LLAMA2_ENDPOINT=your-llama2-endpoint-id
# Your endpoint URL: https://api.runpod.ai/v2/fuep0cdc1bs3an/run
 
#ANhtropic apid key

CLAUDE_API_KEY="************************************************************************************************************"

# HuggingFace Token (OPTIONAL - NOT needed since you're using RunPod Serverless)
HF_TOKEN=*************************************
# Get from: https://huggingface.co/settings/tokens
 
# =============================================================================
# 🗄 DATABASE CONFIGURATION (Required for feedback storage)
# =============================================================================
 
# SAP BTP PostgreSQL (BLOCKED by corporate firewall)
# DATABASE_URL=postgresql://b5ccd24330c6:<EMAIL>:6214/wXQhTPfzjudB?sslmode=require&sslcert=download&sslrootcert=download
 
# Local PostgreSQL (for development)
# DATABASE_URL=postgresql://postgres:password@localhost:5432/iflow_feedback
 
 
# BTP Service Key Components (for reference)
BTP_POSTGRES_HOST=postgres-42a4a134-750a-4715-80e4-70eff2256b1a.cqryblsdrbcs.us-east-1.rds.amazonaws.com
BTP_POSTGRES_PORT=6214
BTP_POSTGRES_USER=b5ccd24330c6
BTP_POSTGRES_PASSWORD=872db628037703f2c89420c13f138e5
BTP_POSTGRES_DATABASE=wXQhTPfzjudB
 
# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379
# Or use cloud Redis:
# REDIS_URL=redis://user:<EMAIL>:6379
 
# =============================================================================
# 🧠 MODEL CONFIGURATION
# =============================================================================
 
# Base model for fine-tuning
BASE_MODEL=codellama/CodeLlama-7b-Python-hf
MODEL_PATH=models/sap-iflow-finetuned
BASE_MODEL_GEMMA3=google/gemma-3-12b-it
 
# =============================================================================
# 🌐 API CONFIGURATION
# =============================================================================
 
API_HOST=0.0.0.0
API_PORT=8000
 
# =============================================================================
# 📊 MONITORING & LOGGING (Optional but recommended)
# =============================================================================
 
# Weights & Biases (for training monitoring)
WANDB_PROJECT=sap-iflow-llmops
WANDB_ENTITY=deepanshanmugam13-it-resonance
WANDB_API_KEY=****************************************
# Get from: https://wandb.ai/settings
 
# =============================================================================
# 💾 LOCAL STORAGE (Simple and sufficient)
# =============================================================================
 
# Local paths for models and data
MODEL_STORAGE_PATH=models/
DATA_STORAGE_PATH=data/
BACKUP_PATH=backups/
 
# =============================================================================
# 🔍 VECTOR DATABASES (Choose one or use multiple)
# =============================================================================
 
# ChromaDB (default - runs locally)
CHROMA_DB_PATH=data/vector_db
 
# Pinecone (cloud vector DB)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
# Get from: https://app.pinecone.io/
 
# Weaviate (alternative)
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key
 
# Qdrant (alternative)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your-qdrant-api-key
 
# =============================================================================
# 🚨 ALERTS & NOTIFICATIONS (Optional)
# =============================================================================
 
# Slack notifications
SLACK_WEBHOOK_URL=your-slack-webhook-url
# Get from: https://api.slack.com/messaging/webhooks
 
# Email notifications
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-email-app-password
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
 
# Error tracking
SENTRY_DSN=your-sentry-dsn
# Get from: https://sentry.io/
 
# =============================================================================
# 🔒 SECURITY (Optional but recommended for production)
# =============================================================================
 
JWT_SECRET_KEY=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here
 
# =============================================================================
# 🛠 DEVELOPMENT SETTINGS
# =============================================================================
 
LOG_LEVEL=INFO
DEBUG_MODE=false
ENABLE_PROFILING=false
 
# =============================================================================
# 💰 COST MANAGEMENT
# =============================================================================
 
# Maximum daily spend on RunPod (in USD)
MAX_DAILY_COST=50.00
MAX_MONTHLY_COST=1000.00
 
#OpenAI API Key

OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Supabase Configuration - TradingApp
# Project: TradingApp (ID: csdzhpskeyqswqmffvxv)
 
SUPABASE_URL=https://csdzhpskeyqswqmffvxv.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNzZHpocHNrZXlxc3dxbWZmdnh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTU5NzIsImV4cCI6MjA2NDA3MTk3Mn0.AmKTGFbDjFgnLIWnKBjBXELLgPrRkYV-pT7N-9apPvs
 
# Optional: Service Role Key (for admin operations)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNzZHpocHNrZXlxc3dxbWZmdnh2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5NTk3MiwiZXhwIjoyMDY0MDcxOTcyfQ.V-V5fyWkd2vPMTGNvrjPLckhYiJxLQ6lvGaFPdnBOT4
 
# Database Connection (for direct PostgreSQL access if needed)
DATABASE_URL=postgresql://postgres.csdzhpskeyqswqmffvxv:<EMAIL>:5432/postgres?sslmode=require
 
 
# Gemma3 Token Limits
GEMMA3_MAX_INPUT_TOKENS=8192
GEMMA3_MAX_OUTPUT_TOKENS=2048
GEMMA3_CHUNK_OVERLAP=200
GEMMA3_MAX_WAIT_TIME=600
 
# Flask Configuration
PORT=5002
HOST=0.0.0.0
DEBUG=True